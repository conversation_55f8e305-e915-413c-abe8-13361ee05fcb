{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  //  "account_id": "",
  "name": "libra-docs",
  "main": ".open-next/worker.js",
  "compatibility_date": "2025-08-03",
  "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],
  "minify": true,
  "assets": {
    "binding": "ASSETS",
    "directory": ".open-next/assets"
  },
  "routes": [
    {
      "pattern": "docs.libra.dev",
      "custom_domain": true
    }
  ],
  "observability": {
    "enabled": true
  },
  /**
   * Smart Placement
   * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
   */
  "placement": { "mode": "smart" },

  /**
   * Bindings
   * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
   * databases, object storage, AI inference, real-time communication and more.
   * https://developers.cloudflare.com/workers/runtime-apis/bindings/
   */

  /**
   * Environment Variables
   * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
   */

  /**
   * Note: Use secrets to store sensitive data.
   * https://developers.cloudflare.com/workers/configuration/secrets/
   */

  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      "service": "libra-docs"
    }
  ],

  // R2 incremental cache
  "r2_buckets": [
    {
      "binding": "NEXT_INC_CACHE_R2_BUCKET",
      "bucket_name": "libra-docs"
    }
  ],

  // DO Queue and DO Sharded Tag Cache
  "durable_objects": {
    "bindings": [
      {
        "name": "NEXT_CACHE_DO_QUEUE",
        "class_name": "DOQueueHandler"
      },
      // This is only required if you use On-demand revalidation
      {
        "name": "NEXT_TAG_CACHE_DO_SHARDED",
        "class_name": "DOShardedTagCache"
      }
    ]
  },

  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": [
        "DOQueueHandler",
        // This is only required if you use On-demand revalidation
        "DOShardedTagCache"
      ]
    }
  ]
}
