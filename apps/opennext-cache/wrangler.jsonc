{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  "main": "src/index.ts",
  "name": "opennext-cache",
  "compatibility_date": "2025-08-03",
  "compatibility_flags": [
    "nodejs_compat"
  ],
  // DO Queue and DO Sharded Tag Cache
  "durable_objects": {
    "bindings": [
      {
        "name": "NEXT_CACHE_DO_QUEUE",
        "class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>"
      },
      {
        "name": "NEXT_TAG_CACHE_DO_SHARDED",
        "class_name": "DOShardedTagCache"
      }
    ]
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": [
        "DOQueueHandler",
        "DOShardedTagCache"
      ]
    }
  ]
}
