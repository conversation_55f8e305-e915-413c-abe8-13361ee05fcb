{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "root": true, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "files": {"ignoreUnknown": true, "maxSize": 5242880, "includes": ["**", "!**/.wrangler/**", "!**/workbox*.js", "!**/sw*.js", "!**/service-worker.js", "!**/fallback*.js", "!**/node_modules/**", "!**/dist/**", "!**/build/**", "!**/ios/**", "!**/.git/**", "!**/.vscode/**", "!**/.android/**", "!**/.DS_Store/**", "!**/Thumbs.db/**", "!**/.next/**", "!**/useCanvasCursor.ts", "!**/privacy/**", "!**/.cache/**", "!**/tsbuildinfo.json", "!idea", "!cloudflare-env.d.ts", "!**/paraglide/**", "!**/public/**", "!**/next-env.d.ts", "!**/payload-types.ts", "!**/app/(payload)/**", "!**/public/inspect.js", "!**/public/inspect.src.js", "!worker-configuration.d.ts"]}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useTemplate": "warn", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "suspicious": {"noEmptyInterface": "off", "noExplicitAny": "off", "noArrayIndexKey": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "a11y": {"noSvgWithoutTitle": "off"}, "performance": {"noDynamicNamespaceImportAccess": "off"}}}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2, "includes": ["**", "!**/node_modules/**/*", "!**/*.config.*", "!**/*.json", "!**/.turbo"]}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded"}}}